# Use the official Node.js runtime as the base image
FROM node:24-alpine AS base

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Enable corepack and install pnpm
RUN corepack enable
RUN corepack prepare pnpm@latest --activate

# Copy package files for dependency installation
# This layer will only be rebuilt when package.json or package-lock.json changes
COPY package.json package-lock.json* ./

# Install dependencies based on the preferred package manager
# Use npm for now since pnpm-lock.yaml doesn't exist yet
RUN npm ci --only=production --ignore-scripts

# Install dev dependencies for build stage
FROM base AS builder-deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Enable corepack and install pnpm
RUN corepack enable
RUN corepack prepare pnpm@latest --activate

# Copy package files for dependency installation
COPY package.json package-lock.json* ./

# Install all dependencies (including dev dependencies for build)
RUN npm ci --ignore-scripts

# Build stage - only rebuilds when source code changes
FROM base AS builder
WORKDIR /app

# Enable corepack and install pnpm
RUN corepack enable
RUN corepack prepare pnpm@latest --activate

# Copy node_modules from builder-deps stage
COPY --from=builder-deps /app/node_modules ./node_modules

# Copy source code - this layer invalidates when any source file changes
COPY . .

# Next.js collects completely anonymous telemetry data about general usage.
# Learn more here: https://nextjs.org/telemetry
# Uncomment the following line in case you want to disable telemetry during the build.
ENV NEXT_TELEMETRY_DISABLED=1

# Build the application
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
# Uncomment the following line in case you want to disable telemetry during runtime.
ENV NEXT_TELEMETRY_DISABLED=1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# server.js is created by next build from the standalone output
# https://nextjs.org/docs/pages/api-reference/next-config-js/output
CMD ["node", "server.js"]
