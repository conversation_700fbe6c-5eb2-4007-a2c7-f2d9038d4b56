'use client'

import React, { useState, useEffect } from 'react'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { AuthModal } from '@/components/auth/AuthModal'
import { useAuth } from '@/lib/auth-context'
import { useToast } from '@/hooks/use-toast'
import { useLoggedFetch } from '@/hooks/use-logged-fetch'
import { useErrorLogger } from '@/lib/utils'
import { 
  FileText, 
  BookOpen, 
  Star, 
  TrendingUp, 
  Calendar,
  Plus,
  ArrowRight,
  BarChart3,
  Clock
} from 'lucide-react'
import Link from 'next/link'
import type { Paper, Collection } from '@/lib/types'

interface DashboardStats {
  totalPapers: number
  totalCollections: number
  starredPapers: number
  recentPapers: number
  papersThisWeek: number
  papersThisMonth: number
}

interface RecentActivity {
  id: string
  type: 'paper_added' | 'paper_starred' | 'collection_created'
  title: string
  date: string
}

function DashboardContent() {
  const { user } = useAuth()
  const { toast } = useToast()
  const { authenticatedFetch } = useLoggedFetch('DashboardContent')
  const { logError } = useErrorLogger('DashboardContent')
  const [stats, setStats] = useState<DashboardStats>({
    totalPapers: 0,
    totalCollections: 0,
    starredPapers: 0,
    recentPapers: 0,
    papersThisWeek: 0,
    papersThisMonth: 0,
  })
  const [recentPapers, setRecentPapers] = useState<Paper[]>([])
  const [recentCollections, setRecentCollections] = useState<Collection[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true)

      // Fetch papers with comprehensive logging
      let papers: Paper[] = []
      try {
        const papersResponse = await authenticatedFetch('/api/papers')
        if (!papersResponse.ok) {
          throw new Error('Failed to fetch papers')
        }
        const papersData = await papersResponse.json()
        papers = papersData.data || papersData || []
      } catch (error) {
        logError('fetch_papers', error)
        toast({
          title: 'Failed to load papers',
          description: 'Please try refreshing the page.',
          variant: 'destructive',
        })
      }

      // Fetch collections with comprehensive logging
      let collections: Collection[] = []
      try {
        const collectionsResponse = await authenticatedFetch('/api/collections')
        if (!collectionsResponse.ok) {
          throw new Error('Failed to fetch collections')
        }
        const collectionsData = await collectionsResponse.json()
        collections = collectionsData.data || collectionsData || []
      } catch (error) {
        logError('fetch_collections', error)
        toast({
          title: 'Failed to load collections',
          description: 'Please try refreshing the page.',
          variant: 'destructive',
        })
      }
      
      // Calculate stats
      const now = new Date()
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      
      const papersThisWeek = papers.filter(p => new Date(p.createdAt) >= oneWeekAgo).length
      const papersThisMonth = papers.filter(p => new Date(p.createdAt) >= oneMonthAgo).length
      
      setStats({
        totalPapers: papers.length,
        totalCollections: collections.length,
        starredPapers: papers.filter(p => p.starred).length,
        recentPapers: papers.filter(p => new Date(p.createdAt) >= oneWeekAgo).length,
        papersThisWeek,
        papersThisMonth,
      })
      
      // Set recent items (last 5)
      setRecentPapers(papers.slice(0, 5))
      setRecentCollections(collections.slice(0, 3))

    } catch (error) {
      // Errors are already logged by the API request utility, but log unexpected errors
      logError('unexpected_dashboard_error', error)
      toast({
        title: 'Failed to load dashboard',
        description: 'Please try refreshing the page.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen">
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <SidebarTrigger />
          <div className="ml-4">
            <h1 className="text-lg font-semibold">Dashboard</h1>
          </div>
        </div>
      </header>

      <main className="flex-1 p-6 space-y-6">
        {/* Welcome Section */}
        <div className="space-y-2">
          <h2 className="text-2xl font-bold">Welcome back, {user?.displayName || 'Researcher'}!</h2>
          <p className="text-muted-foreground">
            Here's an overview of your research progress and recent activity.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Papers</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalPapers}</div>
              <p className="text-xs text-muted-foreground">
                +{stats.papersThisMonth} this month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Collections</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalCollections}</div>
              <p className="text-xs text-muted-foreground">
                Organized research
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Starred Papers</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.starredPapers}</div>
              <p className="text-xs text-muted-foreground">
                Important papers
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Week</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.papersThisWeek}</div>
              <p className="text-xs text-muted-foreground">
                Papers added
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Get started with your research workflow
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Link href="/papers/new">
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Paper
                </Button>
              </Link>
              <Link href="/collections">
                <Button variant="outline">
                  <BookOpen className="mr-2 h-4 w-4" />
                  Browse Collections
                </Button>
              </Link>
              <Link href="/review">
                <Button variant="outline">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Review Papers
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="mr-2 h-4 w-4" />
                Recent Papers
              </CardTitle>
            </CardHeader>
            <CardContent>
              {recentPapers.length > 0 ? (
                <div className="space-y-3">
                  {recentPapers.map((paper) => (
                    <div key={paper.id} className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{paper.title}</p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(paper.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <Link href={`/papers/${paper.id}`}>
                        <Button variant="ghost" size="sm">
                          <ArrowRight className="h-3 w-3" />
                        </Button>
                      </Link>
                    </div>
                  ))}
                  <Link href="/papers">
                    <Button variant="outline" size="sm" className="w-full">
                      View All Papers
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-muted-foreground">No papers yet</p>
                  <Link href="/papers/new">
                    <Button variant="outline" size="sm" className="mt-2">
                      Add Your First Paper
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookOpen className="mr-2 h-4 w-4" />
                Recent Collections
              </CardTitle>
            </CardHeader>
            <CardContent>
              {recentCollections.length > 0 ? (
                <div className="space-y-3">
                  {recentCollections.map((collection) => (
                    <div key={collection.id} className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{collection.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {collection.description || 'No description'}
                        </p>
                      </div>
                      <Link href={`/collections/${collection.id}`}>
                        <Button variant="ghost" size="sm">
                          <ArrowRight className="h-3 w-3" />
                        </Button>
                      </Link>
                    </div>
                  ))}
                  <Link href="/collections">
                    <Button variant="outline" size="sm" className="w-full">
                      View All Collections
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-muted-foreground">No collections yet</p>
                  <Link href="/collections">
                    <Button variant="outline" size="sm" className="mt-2">
                      Create Your First Collection
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}

export default function DashboardPage() {
  const [authModalOpen, setAuthModalOpen] = useState(false)

  return (
    <ProtectedRoute
      onAuthRequired={() => setAuthModalOpen(true)}
      requireEmailVerification={false}
      fallback={
        <AuthModal
          isOpen={true}
          onClose={() => {
            // Redirect to login page instead of just closing
            window.location.href = '/login'
          }}
        />
      }
    >
      <DashboardContent />
    </ProtectedRoute>
  )
}
