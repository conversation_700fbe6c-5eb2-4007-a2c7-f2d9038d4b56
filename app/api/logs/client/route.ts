import { NextRequest, NextResponse } from 'next/server'
import { withPublicSecurity } from '@/lib/security-middleware'

/**
 * API endpoint to receive client-side logs and forward them to Docker container logs
 * Protected with rate limiting to prevent abuse and infinite loops
 */
export const POST = withPublicSecurity(async (request: NextRequest, { correlationId, logger }) => {
  try {
    // Parse request body with size limit
    const logEntry = await request.json()

    // Validate log entry structure
    if (!logEntry || typeof logEntry !== 'object') {
      return NextResponse.json(
        { error: 'Invalid log entry format: must be an object', correlationId },
        { status: 400 }
      )
    }

    if (!logEntry.type || !logEntry.level) {
      return NextResponse.json(
        { error: 'Invalid log entry format: missing type or level', correlationId },
        { status: 400 }
      )
    }

    // Validate log level
    const validLevels = ['error', 'warn', 'info', 'debug']
    if (!validLevels.includes(logEntry.level)) {
      return NextResponse.json(
        { error: 'Invalid log level: must be error, warn, info, or debug', correlationId },
        { status: 400 }
      )
    }

    // Prevent logging of client logging errors to avoid infinite loops
    if (logEntry.type === 'CLIENT_LOGGING_ERROR' ||
        (logEntry.url && logEntry.url.includes('/api/logs/client'))) {
      return NextResponse.json({ success: true, skipped: true })
    }

    // Add client-side indicator to the log entry
    const enhancedLogEntry = {
      level: logEntry.level,
      message: `Frontend ${logEntry.type}: ${logEntry.message || 'No message'}`,
      timestamp: new Date().toISOString(),
      source: 'frontend',
      correlationId,
      userAgent: request.headers.get('user-agent'),
      clientIp: request.headers.get('x-forwarded-for') ||
                request.headers.get('x-real-ip') ||
                'unknown',
      // Only include safe fields to prevent log injection
      type: logEntry.type,
      component: logEntry.component,
      action: logEntry.action,
      requestId: logEntry.requestId,
      userId: logEntry.userId,
      status: logEntry.status,
      errorType: logEntry.errorType,
      errorMessage: logEntry.errorMessage?.substring(0, 500), // Limit message length
      url: logEntry.url,
      method: logEntry.method,
      responseTime: logEntry.responseTime
    }

    // Log to server console - this will appear in Docker logs
    // Use the structured logger to ensure proper formatting
    if (logEntry.level === 'error') {
      logger.error('Frontend Error', enhancedLogEntry)
    } else if (logEntry.level === 'warn') {
      logger.warn('Frontend Warning', enhancedLogEntry)
    } else {
      logger.info('Frontend Log', enhancedLogEntry)
    }

    return NextResponse.json({ success: true, correlationId })
  } catch (error: any) {
    // Don't use the logger here to avoid potential infinite loops
    // Just log directly to console with minimal information
    console.error('Client logging endpoint error:', {
      error: error.message,
      correlationId,
      timestamp: new Date().toISOString()
    })

    return NextResponse.json(
      { error: 'Failed to process log entry', correlationId },
      { status: 500 }
    )
  }
}, {
  // Validation options
  maxBodySize: 2048 // 2KB limit for log entries
}, {
  // Rate limiting: more restrictive for logging endpoint
  maxRequests: 50,
  windowMs: 60 * 1000, // 1 minute
  message: 'Client logging rate limit exceeded'
})
