'use client'

import React, { useState, useEffect } from 'react'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { AuthModal } from '@/components/auth/AuthModal'
import { useAuth } from '@/lib/auth-context'
import { useToast } from '@/hooks/use-toast'
import { useLoggedFetch } from '@/hooks/use-logged-fetch'
import { 
  Users, 
  Search, 
  MoreHorizontal,
  Shield,
  UserCheck,
  UserX,
  Lock,
  Unlock,
  Key,
  Trash2,
  ChevronLeft,
  ChevronRight,
  ArrowUpDown
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import Link from 'next/link'
import { UserDetailModal } from '@/components/admin/UserDetailModal'

interface User {
  id: string
  email: string
  displayName?: string
  role: 'admin' | 'user' | 'readonly'
  emailVerified: boolean
  isActive: boolean
  createdAt: string
  lastLogin?: string
  paperCount: number
  collectionCount: number
}

interface UsersResponse {
  data: User[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  sorting: {
    sortBy: string
    sortOrder: 'asc' | 'desc'
  }
}

function AdminUsersContent() {
  const { user } = useAuth()
  const { toast } = useToast()
  const { authenticatedFetch } = useLoggedFetch('AdminUsersContent')
  const [users, setUsers] = useState<User[]>([])
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  })
  const [sorting, setSorting] = useState({
    sortBy: 'createdAt',
    sortOrder: 'desc' as 'asc' | 'desc'
  })
  const [search, setSearch] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null)

  useEffect(() => {
    fetchUsers()
  }, [pagination.page, sorting.sortBy, sorting.sortOrder, search])

  const fetchUsers = async () => {
    setIsLoading(true)

    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        sortBy: sorting.sortBy,
        sortOrder: sorting.sortOrder,
        ...(search && { search })
      })

      const response = await authenticatedFetch(`/api/admin/users?${params}`)

      if (!response.ok) {
        throw new Error('Failed to fetch users')
      }

      const data: UsersResponse = await response.json()
      setUsers(data.data)
      setPagination(data.pagination)
      setSorting(data.sorting)
    } catch (error) {
      console.error('AdminUsersContent: Failed to fetch users:', error)
      toast({
        title: 'Failed to load users',
        description: 'Please try refreshing the page.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSort = (field: string) => {
    setSorting(prev => ({
      sortBy: field,
      sortOrder: prev.sortBy === field && prev.sortOrder === 'asc' ? 'desc' : 'asc'
    }))
  }

  const handleSearch = (value: string) => {
    setSearch(value)
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
  }

  const handleUserAction = async (userId: string, action: string, data?: any) => {
    try {
      let response
      switch (action) {
        case 'delete':
          response = await authenticatedFetch(`/api/admin/users/${userId}`, {
            method: 'DELETE'
          })
          break
        case 'lock':
          response = await authenticatedFetch(`/api/admin/users/${userId}/lock`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ locked: true, reason: 'Locked by admin' })
          })
          break
        case 'unlock':
          response = await authenticatedFetch(`/api/admin/users/${userId}/lock`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ locked: false })
          })
          break
        default:
          return
      }

      if (!response.ok) {
        throw new Error(`Failed to ${action} user`)
      }

      toast({
        title: 'Success',
        description: `User ${action}ed successfully`,
      })

      fetchUsers() // Refresh the list
    } catch (error) {
      console.error(`Failed to ${action} user:`, error)
      toast({
        title: 'Error',
        description: `Failed to ${action} user`,
        variant: 'destructive',
      })
    }
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'admin':
        return <Badge variant="destructive">Admin</Badge>
      case 'user':
        return <Badge variant="default">User</Badge>
      case 'readonly':
        return <Badge variant="secondary">Read Only</Badge>
      default:
        return <Badge variant="outline">{role}</Badge>
    }
  }

  const getStatusBadge = (isActive: boolean, emailVerified: boolean) => {
    if (!isActive) {
      return <Badge variant="destructive">Locked</Badge>
    }
    if (!emailVerified) {
      return <Badge variant="secondary">Unverified</Badge>
    }
    return <Badge variant="default" className="bg-green-500">Active</Badge>
  }

  if (isLoading && users.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen">
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <SidebarTrigger />
          <div className="ml-4 flex items-center gap-2">
            <Shield className="h-5 w-5 text-blue-500" />
            <h1 className="text-lg font-semibold">User Management</h1>
          </div>
        </div>
      </header>

      <main className="flex-1 p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Users</h2>
            <p className="text-muted-foreground">
              Manage user accounts and permissions
            </p>
          </div>
          <Link href="/admin">
            <Button variant="outline">
              Back to Dashboard
            </Button>
          </Link>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Search Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by email or name..."
                value={search}
                onChange={(e) => handleSearch(e.target.value)}
                className="max-w-sm"
              />
            </div>
          </CardContent>
        </Card>

        {/* Users Table */}
        <Card>
          <CardHeader>
            <CardTitle>Users ({pagination.total})</CardTitle>
            <CardDescription>
              All registered users in the system
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>
                    <Button variant="ghost" onClick={() => handleSort('email')}>
                      Email <ArrowUpDown className="ml-2 h-4 w-4" />
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button variant="ghost" onClick={() => handleSort('displayName')}>
                      Name <ArrowUpDown className="ml-2 h-4 w-4" />
                    </Button>
                  </TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Content</TableHead>
                  <TableHead>
                    <Button variant="ghost" onClick={() => handleSort('createdAt')}>
                      Created <ArrowUpDown className="ml-2 h-4 w-4" />
                    </Button>
                  </TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((userData) => (
                  <TableRow key={userData.id}>
                    <TableCell className="font-medium">{userData.email}</TableCell>
                    <TableCell>{userData.displayName || '-'}</TableCell>
                    <TableCell>{getRoleBadge(userData.role)}</TableCell>
                    <TableCell>{getStatusBadge(userData.isActive, userData.emailVerified)}</TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {userData.paperCount}P, {userData.collectionCount}C
                      </div>
                    </TableCell>
                    <TableCell>
                      {new Date(userData.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => setSelectedUserId(userData.id)}>
                            <UserCheck className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Key className="mr-2 h-4 w-4" />
                            Reset Password
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          {userData.isActive ? (
                            <DropdownMenuItem 
                              onClick={() => handleUserAction(userData.id, 'lock')}
                              className="text-orange-600"
                            >
                              <Lock className="mr-2 h-4 w-4" />
                              Lock Account
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem 
                              onClick={() => handleUserAction(userData.id, 'unlock')}
                              className="text-green-600"
                            >
                              <Unlock className="mr-2 h-4 w-4" />
                              Unlock Account
                            </DropdownMenuItem>
                          )}
                          {userData.id !== user?.id && (
                            <DropdownMenuItem 
                              onClick={() => handleUserAction(userData.id, 'delete')}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete User
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {/* Pagination */}
            <div className="flex items-center justify-between space-x-2 py-4">
              <div className="text-sm text-muted-foreground">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                {pagination.total} users
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.totalPages}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>

      {/* User Detail Modal */}
      <UserDetailModal
        userId={selectedUserId}
        isOpen={selectedUserId !== null}
        onClose={() => setSelectedUserId(null)}
        onUserUpdated={fetchUsers}
      />
    </div>
  )
}

export default function AdminUsersPage() {
  const [authModalOpen, setAuthModalOpen] = useState(false)

  return (
    <ProtectedRoute
      onAuthRequired={() => setAuthModalOpen(true)}
      requireEmailVerification={false}
      requiredRoles={['admin']}
      fallback={
        <AuthModal
          isOpen={true}
          onClose={() => {
            window.location.href = '/login'
          }}
        />
      }
    >
      <AdminUsersContent />
    </ProtectedRoute>
  )
}
